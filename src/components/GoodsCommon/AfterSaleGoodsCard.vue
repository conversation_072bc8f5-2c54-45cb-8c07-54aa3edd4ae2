<template>
  <div class="goods-item" id="goods-item" :class="{ 'has-actions': showActions }"
    :style="{ minHeight: minHeight + 'px' }">

    <div class="goods-detail">
      <div class="multiple-goods-images" v-if="isMultipleGoods">
        <div class="images-scroll-container">
          <div v-for="(goodsItem, index) in multiGoodsData" :key="index" class="single-goods-image" :style="imageStyle">
            <img :src="getImageUrl(goodsItem.detailImageUrl)" :alt="goodsItem.name" />
          </div>
        </div>
      </div>
      <div v-else class="single-goods-image" :style="imageStyle">
        <img :src="getImageUrl(singleGoodsData.detailImageUrl)" :alt="item.name" />
      </div>
      <div class="goods-info" :class="{ 'single-goods-flex': !isMultipleGoods }">
        <div class="goods-info-left" v-if="!isMultipleGoods">
          <div class="goods-name">{{ singleGoodsData.name }}</div>
          <div class="goods-spec">{{ singleGoodsData.params }}</div>
        </div>
        <div class="goods-info-right">
          <PriceDisplay :price="displayPrice" size="small" class="goods-price" />
          <span class="goods-quantity">共{{ displayQuantity }}件</span>
        </div>
      </div>
    </div>

    <div v-if="showActions" class="goods-action">
      <div v-if="showTips" class="action-group-left">
        <slot name="tips" :item="item">
          <!-- 默认操作按钮 -->
        </slot>
      </div>
      <div class="action-group-right">
        <slot name="actions" :item="item">
          <!-- 默认操作按钮 -->
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import { useGlobalPopover } from '@/composables/index.js'
import { compact } from 'lodash-es'
const props = defineProps({
  // 商品项数据
  item: {
    type: Object,
    required: true
  },
  itemId: {
    type: [String, Number],
    default: Math.random()
  },
  // 图片尺寸
  imageSize: {
    type: Number,
    default: 90
  },
  // 最小高度
  minHeight: {
    type: Number,
    default: 135
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: false
  },
  // 是否显示提示按钮
  showTips: {
    type: Boolean,
    default: true
  },
})

// 解构 props
const { item, itemId, imageSize, moreActions } = props

const emit = defineEmits(['select-action'])

// 使用全局popover状态
const { setActivePopover, closeAllPopovers, isPopoverActive } = useGlobalPopover()

const getImageUrl = (imageUrl) => {
  if (Array.isArray(imageUrl)) {
    return imageUrl.length > 0 ? imageUrl[0] : ''
  }
  return imageUrl || ''
}

// 计算商品列表
const goodsList = computed(() => {
  const { skuNumInfoList, skuList } = item
  const list = skuNumInfoList || skuList

  if (!list || !Array.isArray(list)) {
    return []
  }

  return list.map(listItem => {
    if (listItem.sku) {
      // 新数据结构，字段拍平
      return { ...listItem.sku, skuNum: listItem.skuNum }
    } else {
      // 老数据结构
      return { ...listItem, skuNum: '1' }
    }
  })
})


// 多商品数据 - 如果是多个商品，返回完整列表；如果是单个商品，返回第一个元素
const multiGoodsData = computed(() => {
  const list = goodsList.value
  if (list.length > 1) {
    // 多商品情况，返回完整列表
    return list
  } else if (list.length === 1) {
    // 单商品情况，返回第一个元素
    return list[0]
  }
  return null
})

// 单商品数据 - 始终返回第一个商品数据
const singleGoodsData = computed(() => {
  const list = goodsList.value
  if (list.length > 0) {
    const item = list[0]
    // 添加params字段，类似于GoodsList.vue中的处理
    item.params = compact([item.param, item.param1, item.param2, item.param3, item.param4]).join(' ')
    return item
  }
  return null
})



// 计算当前popover是否应该显示
const isPopoverVisible = computed({
  get() {
    return isPopoverActive(itemId)
  },
  set(value) {
    if (value) {
      setActivePopover(itemId)
    } else {
      closeAllPopovers()
    }
  }
})



// 计算图片样式
const imageStyle = computed(() => ({
  width: imageSize + 'px',
  height: imageSize + 'px'
}))

// 处理更多操作选择
const onSelectAction = (action, index) => {
  console.log('Selected action:', action, 'moreActions:', moreActions)
  emit('select-action', action, index, item)
  closeAllPopovers()
}

// 判断是否为多商品
const isMultipleGoods = computed(() => {
  // 优先检查 skuNumInfoList
  if (Array.isArray(item.skuNumInfoList)) {
    return item.skuNumInfoList.length > 1
  }
  // 其次检查 skuList
  if (Array.isArray(item.skuList)) {
    return item.skuList.length > 1
  }
  // 都不存在时返回 false
  return false
})

// 显示价格
const displayPrice = computed(() => {
  if (isMultipleGoods.value) {
    // 多商品时显示总价
    return item.price || item.totalPrice
  }
  return item.price
})

// 显示数量
const displayQuantity = computed(() => {
  const list = goodsList.value

  if (isMultipleGoods.value) {
    // 多商品时累加所有商品的skuNum
    return list.reduce((sum, goods) => sum + (parseInt(goods.skuNum) || 0), 0)
  } else {
    // 单商品时返回第一个商品的skuNum
    return list.length > 0 ? (parseInt(list[0].skuNum) || 0) : 0
  }
})

</script>

<style scoped lang="less">
.goods-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 20px;

  :deep(.van-popover__action) {
    width: 110px;
    height: 32px;
  }


  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .goods-detail {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .multiple-goods-images {
    width: 100%;
    margin-right: 10px;
    flex: 1;
    overflow-x: auto;
    scroll-behavior: smooth;

    .images-scroll-container {
      width: 100%;
      display: flex;
      gap: 2px;

      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        height: 2px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 1px;
      }

      .single-goods-image {
        border-radius: @radius-4;
        overflow: hidden;
        flex-shrink: 0;
        margin-right: 5px;

        &:last-child {
          margin-right: 0;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  .single-goods-image {
    border-radius: @radius-4;
    overflow: hidden;
    margin-right: 10px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .goods-info {
    display: flex;
    justify-content: flex-end;
    flex: 1;

    &.single-goods-flex {
      flex: 1;
    }

    .goods-info-left {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-right: 20px;
      flex-shrink: 0;

      .goods-name {
        font-size: @font-size-13;
        color: @text-color-primary;
        line-height: 1.5;
        margin-bottom: 7px;
        .multi-ellipsis(2);
      }

      .goods-spec {
        font-size: @font-size-11;
        color: @text-color-tertiary;
        margin-bottom: 7px;
      }
    }

    .goods-info-right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .goods-price {
        margin-bottom: 7px;
      }

      .goods-quantity {
        font-size: @font-size-11;
        color: @text-color-tertiary;
        margin-bottom: 7px;
      }
    }
  }

  .logistics-info {
    width: 100%;
    margin: 7px 0;

    .logistics-content {
      display: flex;
      align-items: center;
      font-size: @font-size-11;
      color: @text-color-tertiary;
      line-height: 1.5;
      padding: 7px;
      box-sizing: border-box;
      background: #F5F6F7;
      border-radius: @radius-4;

      .logistics-label {
        margin-right: 4px;
        color: @text-color-secondary;
      }

      .logistics-text {
        margin-right: 8px;
        color: @text-color-primary;
      }

      .logistics-company,
      .logistics-number {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .goods-action {
    width: 100%;
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;

    .more-action {
      flex-shrink: 0;
    }

    .more-button {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #666;
      cursor: pointer;
    }

    .action-group-left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }

    .action-group-right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }

  }
}
</style>
