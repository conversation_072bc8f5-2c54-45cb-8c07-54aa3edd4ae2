<template>
  <header class="search-header">
    <div class="search-header__input-wrapper" @click="handleInputClick">
      <!-- 搜索图标插槽 -->
      <slot name="search-icon">
        <img
          src="@/static/images/search.png"
          alt="搜索"
          class="search-header__icon"
          loading="lazy"
        />
      </slot>
      <input
        ref="inputRef"
        v-model="keyword"
        type="text"
        class="search-header__input"
        :placeholder="placeholder"
        :readonly="redirectToSearch"
        @keyup.enter="debouncedSearch"
      />
      <button
        type="button"
        class="search-header__button"
        @click.stop="debouncedSearch"
      >
        搜索
      </button>
    </div>
    <!-- 右侧操作区插槽 -->
    <slot name="right-action" />
  </header>
</template>

<script setup>
import { ref, watch, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'

const router = useRouter()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '搜索'
  },
  redirectToSearch: {
    type: Boolean,
    default: false
  },
  redirectUrl: {
    type: String,
    default: '/search'
  }
})

const { modelValue, placeholder, redirectToSearch, redirectUrl } = toRefs(props)

const emit = defineEmits(['update:modelValue', 'search', 'clickable'])

// 响应式数据
const keyword = ref(modelValue.value)
const inputRef = ref(null)

// 监听modelValue变化
watch(modelValue, (newVal) => {
  keyword.value = newVal
}, { immediate: true })

// 监听内部值变化
watch(keyword, (newVal) => {
  emit('update:modelValue', newVal)
})

// 防抖搜索处理函数 - 性能优化
const debouncedSearch = debounce(() => {
  const trimmedKeyword = keyword.value?.trim()
  if (trimmedKeyword) {
    emit('search', trimmedKeyword)
  }
}, 300)

// 处理输入框点击事件
const handleInputClick = () => {
  if (redirectToSearch.value) {
    // 如果需要跳转，则跳转到指定页面
    router.push(redirectUrl.value)
  } else {
    // 如果不需要跳转，则聚焦输入框
    inputRef.value?.focus()
  }
  // 触发点击事件，供外部监听
  emit('clickable')
}

// 暴露给父组件的方法和属性
defineExpose({
  inputRef,
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur()
})
</script>

<style scoped lang="less">
.search-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: @bg-color-white;
  box-sizing: border-box;

  &__input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    height: 32px;
    background-color: @bg-color-white;
    border-radius: 30px;
    padding: 3px 3px 3px 11px;
    box-sizing: border-box;
    border: 1px solid @divider-color-base;
    transition: border-color 0.2s ease;

    //&:focus-within {
    //  border-color: @theme-color;
    //}
  }

  &__icon {
    width: 13px;
    height: 13px;
    margin-right: 6px;
    flex-shrink: 0;
    image-rendering: -webkit-optimize-contrast;
  }

  &__input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: @font-size-13;
    color: @text-color-primary;
    outline: none;
    min-width: 0;

    &::placeholder {
      color: @text-color-tertiary;
    }

    &::-webkit-input-placeholder {
      color: @text-color-tertiary;
    }

    &::-moz-placeholder {
      color: @text-color-tertiary;
    }
  }

  &__button {
    width: 50px;
    height: 26px;
    background-image: @gradient-orange-115;
    border-radius: @radius-15;
    font-size: @font-size-13;
    font-weight: @font-weight-500;
    text-align: center;
    line-height: 26px;
    color: @text-color-white;
    border: none;
    cursor: pointer;
    flex-shrink: 0;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

@media (max-width: 375px) {
  .search-header {
    padding: 8px;

    &__input-wrapper {
      height: 30px;
      padding: 2px 2px 2px 10px;
    }

    &__button {
      width: 45px;
      height: 24px;
      line-height: 24px;
      font-size: @font-size-12;
    }

    &__icon {
      width: 12px;
      height: 12px;
      margin-right: 5px;
    }
  }
}
</style>
