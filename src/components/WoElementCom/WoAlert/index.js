import Alert from './Alert.vue'
import { createVNode, render } from 'vue'

function createInstance(options) {
  // 创建容器
  const container = document.createElement('div')
  document.body.appendChild(container)

  // 创建vnode
  const vnode = createVNode(Alert, {
    ...options,
    onConfirm: () => instance.onConfirmCallback && instance.onConfirmCallback(),
    onCancel: () => instance.onCancelCallback && instance.onCancelCallback()
  })

  // 渲染vnode
  render(vnode, container)

  // 获取组件实例
  const instance = vnode.component.exposed

  // 添加销毁方法
  instance.destroy = () => {
    render(null, container)
    document.body.removeChild(container)
  }

  return instance
}

function alert(options) {
  if (typeof options === 'string') {
    options = { message: options }
  }

  const instance = createInstance(options)
  instance.show()

  // 返回Promise，可以通过then/catch处理确认和取消事件
  return new Promise((resolve, reject) => {
    instance.onConfirmCallback = () => {
      resolve()
      instance.close()
      setTimeout(() => {
        instance.destroy()
      }, 300)
    }

    instance.onCancelCallback = () => {
      resolve()
      instance.close()
      setTimeout(() => {
        instance.destroy()
      }, 300)
    }
  })
}

// 插件安装方法
const install = (app) => {
  app.component(Alert.name, Alert)
  app.config.globalProperties.$alert = alert
}

// 导出插件对象
export default {
  install
}

// 导出组件和方法，方便按需引入
export { Alert, alert }
