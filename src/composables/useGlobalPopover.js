import { ref } from 'vue'
import { createGlobalState } from '@vueuse/core'

export const useGlobalPopover = createGlobalState(() => {
  const activePopoverId = ref(null)

  const setActivePopover = (id) => {
    activePopoverId.value = id
  }

  const closeAllPopovers = () => {
    activePopoverId.value = null
  }

  const isPopoverActive = (id) => {
    return activePopoverId.value === id
  }

  return {
    activePopoverId,
    setActivePopover,
    closeAllPopovers,
    isPopoverActive
  }
})
