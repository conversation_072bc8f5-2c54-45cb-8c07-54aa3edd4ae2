import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
import { getBizCode } from '@utils/curEnv.js'
import { addOneClick } from '@api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { pick, compact, debounce } from 'lodash-es'

export function useGoodsList() {
  const userStore = useUserStore()
  const cartStore = useNewCartStore()
  const router = useRouter()

  // 基础状态
  const goodsList = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const isLoading = ref(true)
  const pageNo = ref(1)
  const pageSize = ref(10)

  // 筛选条件
  const filterCriteria = ref({
    isStock: false,
    minPrice: '',
    maxPrice: '',
    brandsList: []
  })

  // 计算属性
  const hasFilterConditions = computed(() => {
    const { isStock, minPrice, maxPrice, brandsList } = filterCriteria.value
    return isStock ||
      minPrice !== '' ||
      maxPrice !== '' ||
      brandsList.some(brand => brand.isSelected)
  })

  const curAddrInfo = computed(() => userStore.curAddressInfo)

  const locationText = computed(() => {
    return curAddrInfo.value?.addrDetail || ''
  })

  const addressInfo = computed(() => {
    const addressFields = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName', 'townId', 'townName']
    return JSON.stringify(pick(curAddrInfo.value, addressFields))
  })

  // 重置列表状态
  const resetList = () => {
    pageNo.value = 1
    finished.value = false
    goodsList.value = []
  }

  // 处理商品数据
  const processGoodsData = (items) => {
    return items.map(item => {
      // 处理图片URL
      if (item.imageUrl) {
        item.showImageUrl = item.imageUrl.split(',')[0]
      } else if (item.listImageUrl) {
        item.showImageUrl = item.listImageUrl
      }

      // 处理参数
      item.params = compact([
        item.param,
        item.param1,
        item.param2,
        item.param3,
        item.param4
      ])

      // 处理销量
      if (item.sales !== undefined) {
        item.realSaleVolume = item.sales
      } else if (item.realSaleVolume === undefined) {
        item.realSaleVolume = 0
      }

      return item
    })
  }

  // 应用库存筛选
  const applyStockFilter = (items) => {
    if (filterCriteria.value.isStock) {
      return items.filter(item => item.stock > 0)
    }
    return items
  }

  // 跳转到商品详情
  const goToDetail = (item) => {
    router.push(`/goodsdetail/${item.goodsId}/${item.skuId}`)
  }

  // 跳转到购物车
  const goToCart = () => {
    router.push('/cart')
  }

  // 一键加入购物车
  const addOneCart = debounce(async (item) => {
    try {
      showLoadingToast()

      const [err] = await addOneClick({
        bizCode: getBizCode('ORDER'),
        skuId: item.skuId,
        goodsId: item.goodsId,
        addressInfo: addressInfo.value
      })

      closeToast()

      if (err) {
        showToast(err.msg)
        return
      }

      showToast('加入购物车成功！')
      await cartStore.query()
    } catch (error) {
      closeToast()
      console.error('加入购物车失败:', error)
      showToast('加入购物车失败，请重试')
    }
  }, 300)

  // 筛选处理
  const handleFilterConfirm = (criteria, fetchFunction) => {
    // 如果传入了筛选条件，可以在这里处理
    if (criteria) {
      // 这里可以根据需要处理筛选条件
      console.log('筛选条件:', criteria)
    }

    resetList()
    if (typeof fetchFunction === 'function') {
      fetchFunction()
    } else {
      console.warn('handleFilterConfirm: fetchFunction is not provided or not a function')
    }
  }

  const handleFilterReset = () => {
    console.log('重置筛选条件')
  }

  const handleAddressChanged = (fetchFunction) => {
    resetList()
    if (typeof fetchFunction === 'function') {
      fetchFunction()
    } else {
      console.warn('handleAddressChanged: fetchFunction is not provided or not a function')
    }
  }

  return {
    // 状态
    goodsList,
    loading,
    finished,
    isLoading,
    pageNo,
    pageSize,
    filterCriteria,

    // 计算属性
    hasFilterConditions,
    curAddrInfo,
    locationText,
    addressInfo,

    // 方法
    resetList,
    processGoodsData,
    applyStockFilter,
    goToDetail,
    goToCart,
    addOneCart,
    handleFilterConfirm,
    handleFilterReset,
    handleAddressChanged
  }
}
