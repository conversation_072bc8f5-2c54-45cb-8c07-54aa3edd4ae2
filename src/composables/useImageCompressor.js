import compressImageFile from '@utils/imgZip.js'
import { ref } from 'vue'

// 图片压缩Hooks
export function useImageCompressor() {
  const loading = ref(false)
  const result = ref(null)
  const error = ref(null)

  const compress = async (file, options = {}) => {
    loading.value = true
    error.value = null
    try {
      const res = await compressImageFile({ file, ...options })
      result.value = res
      loading.value = false
      return res
    } catch (err) {
      error.value = err
      loading.value = false
      return null
    }
  }

  const beforeRead = async (file, options = {}) => {
    const {
      maxSize = 10,
      showLoadingMessage = true,
      loadingMessage = '处理中...',
      showToast,
      showLoadingToast,
      closeToast
    } = options

    if (!showToast || !showLoadingToast || !closeToast) {
      throw new Error('showToast, showLoadingToast, and closeToast are required')
    }

    const fileSizeBefore = file.size / 1024 / 1024
    console.warn('fileSizeBefore', fileSizeBefore.toFixed(2) + 'M')

    if (showLoadingMessage) {
      showLoadingToast({
        message: loadingMessage,
        forbidClick: true
      })
    } else {
      showLoadingToast()
    }

    const startTime = new Date()
    const newFileObj = await compressImageFile({ file })
    const endTime = new Date()
    const executionTime = (endTime - startTime) / 1000
    console.log(`压缩结束执行时间为：${executionTime}秒`)
    closeToast()

    const { file: newFile } = newFileObj
    const fileSizeAfter = newFile.size / 1024 / 1024
    console.warn('fileSizeAfter', fileSizeAfter.toFixed(2) + 'M')

    if (fileSizeAfter > maxSize) {
      showToast(`图片大小不能超过 ${maxSize}M`)
      return false
    }

    console.warn('fileSizeBefore', fileSizeBefore)

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
    if (newFile instanceof Array && newFile.length) {
      if (!allowedTypes.includes(newFile.type)) {
        showToast('请选择正确图片格式上传')
        return false
      }
      return newFile
    } else {
      if (!allowedTypes.includes(newFile.type)) {
        showToast('请选择正确图片格式上传')
        return false
      }
      return newFile
    }
  }

  return {
    loading,
    result,
    error,
    compress,
    beforeRead
  }
}
