<template>
  <div class="after-sales-order-item">
    <WoCard>
      <header class="after-sales-order-item__header">
        <div class="after-sales-order-item__number-container">
          <span class="after-sales-order-item__number-text">
            服务单号：{{ orderData.afterSaleId || '暂无服务单号' }}
          </span>
          <img
            v-if="orderData.afterSaleId"
            src="@/static/images/copy.png"
            alt="复制"
            class="after-sales-order-item__copy-icon"
            loading="lazy"
            @click.stop="handleCopyOrderNumber(orderData.id)"
          />
        </div>
        <div class="after-sales-order-item__status">
          <div v-if="orderData.applyType" class="after-sales-order-item__apply-status">
            <i class="goods-info-status-base" :class="afterSalesStatusClass"></i>
            <span>{{ afterSalesStatusText }}</span>
          </div>
        </div>
      </header>
      <section class="after-sales-order-item__goods">
        <AfterSaleGoodsCard
          :item="orderData"
          :image-size="75"
          :min-height="110"
          :show-actions="true"
          :item-id="orderData.id"
        >
          <template #tips>
            <p class="tips" v-if="!isExpires && !afterSaleId && orderData.orderState !== '10'">
              该商品已超过售后期 <i class="tips-icon" @click="showTipsDetail"></i>
            </p>
            <p class="tips" v-else>{{ orderStateText }}</p>
          </template>
          <template #actions>
            <WoButton
              v-for="action in actionButtons"
              :key="action.key"
              :type="action.type || 'primary'"
              size="small"
              @click.stop="action.handler"
            >
              {{ action.label }}
            </WoButton>
          </template>
        </AfterSaleGoodsCard>
      </section>
    </WoCard>

    <!-- 售后过期提示弹窗 -->
    <Popup
      class="popup after-sales-expiration-popup"
      :style="{ minHeight: '240px' }"
      safe-area-inset-bottom
      lock-scroll
      round
      position="bottom"
      v-model:show="afterSalesExpirationPopupShow"
    >
      <div class="popup-header">
        <p class="title"></p>
        <img @click="popupClose" class="close" src="../assets/popupClose.png" alt="" srcset="">
      </div>
      <div class="popup-content">
        <div class="after-sales-expiration-content">
          <p class="after-sales-expiration-tips">抱歉，订单已过售后申请时效</p>
          <p class="after-sales-expiration-sub-tips">商品已超过售后期限，如需售后可联系客服处理</p>
        </div>
      </div>
      <div class="popup-op">
        <div class="popup-op-btn" @click="afterSalesExpirationPopupShow = false">
          确定
        </div>
      </div>
    </Popup>
  </div>
</template>

<script setup>
import { computed, toRefs, ref } from 'vue'
import { debounce } from 'lodash-es'
import useClipboard from 'vue-clipboard3'
import { showToast, Popup } from 'vant'
import dayjs from 'dayjs'
import WoCard from '@components/WoElementCom/WoCard.vue'
import AfterSaleGoodsCard from '@components/GoodsCommon/AfterSaleGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import orderState from '@/utils/orderState.js'

const props = defineProps({
  orderData: {
    type: Object,
    required: true
  },
  actionButtons: {
    type: Array,
    default: () => []
  }
})

const { orderData, actionButtons } = toRefs(props)
const { toClipboard } = useClipboard()

// 售后过期弹窗显示状态
const afterSalesExpirationPopupShow = ref(false)

// 售后ID
const afterSaleId = computed(() => {
  return orderData.value?.afterSaleId
})

// 判断是否在售后有效期内
const isExpires = computed(() => {
  const expireTimeDateStr = orderData.value?.expireTime
  if (!expireTimeDateStr) return false

  const expDate = dayjs(expireTimeDateStr)
  const now = dayjs()
  // 当前订单是否在有效期内 false为非有效期，true为有效期
  return expDate > now
})

const orderStateText = computed(() => {
  const status = orderData.value?.orderState
  switch (status) {
    case '10':
      return '订单已退款'
    default:
      return ''
  }
})

// 售后状态文字
const afterSalesStatusText = computed(() => {
  const status = orderData.value?.applyType
  switch (status) {
    case '1':
      return '退款'
    case '2':
      return '退货'
    case '3':
      return '换货'
    case '4':
      return '维修'
    case '5':
      return '补发商品'
    default:
      return ''
  }
})

// 售后状态图标样式类
const afterSalesStatusClass = computed(() => {
  const status = orderData.value?.applyType
  switch (status) {
    case '1':
      return 'goods-info-status-icon-refund_only'
    case '2':
      return 'goods-info-status-icon-return_of_goods'
    case '3':
      return 'goods-info-status-icon-exchange_goods'
    case '4':
      return 'goods-info-status-icon-maintenance'
    case '5':
      return 'goods-info-status-icon-reissue_goods'
    default:
      return ''
  }
})

const handleCopyOrderNumber = debounce(async (orderNumber) => {
  try {
    await toClipboard(String(orderNumber))
    showToast('复制成功')
  } catch (e) {
    console.error('复制失败:', e)
    showToast('复制失败')
  }
}, 300)

// 显示售后过期提示详情
const showTipsDetail = () => {
  afterSalesExpirationPopupShow.value = true
}

// 关闭弹窗
const popupClose = () => {
  afterSalesExpirationPopupShow.value = false
}
</script>

<style scoped lang="less">
.after-sales-order-item {
  margin-bottom: 10px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__number-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__number-text {
    font-size: @font-size-11;
    color: @text-color-secondary;
    margin-right: 3px;
    .ellipsis();
  }

  &__copy-icon {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;
    color: @text-color-primary;
  }

  &__apply-status {
    display: flex;
    align-items: center;
    font-size: 26px;

    .goods-info-status-base {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 5px;
      background-repeat: no-repeat;
      background-size: contain;

      &.goods-info-status-icon-reissue_goods {
        background-image: url("../assets/reissue-goods.png");
      }

      &.goods-info-status-icon-exchange_goods {
        background-image: url("../assets/exchange-goods.png");
      }

      &.goods-info-status-icon-refund_only {
        background-image: url("../assets/refund-only.png");
      }

      &.goods-info-status-icon-return_of_goods {
        background-image: url("../assets/return-of-goods.png");
      }

      &.goods-info-status-icon-maintenance {
        background-image: url("../assets/maintenance.png");
      }
    }

    span {
      font-size: @font-size-12;
      color: #879099;
      font-weight: 400;
    }
  }

  &__goods {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 提示样式
.tips {
  display: flex;
  align-items: center;
  color: #879099;
  text-align: left;
  font-weight: 400;
  font-size: @font-size-12;
  vertical-align: middle;
  .tips-icon {
    display: inline-block;
    vertical-align: middle;
    width: 15px;
    height: 15px;
    background-image: url("../assets/question.png");
    background-repeat: no-repeat;
    background-size: contain;
    cursor: pointer;
  }
}

// 弹窗样式
.popup {
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .title {
      flex: 1;
      font-size: 17px;
      color: #171E24;
      text-align: center;
      line-height: 1;
      font-weight: 400;
    }

    .close {
      width: 14px;
      height: 14px;
      cursor: pointer;
    }
  }

  .popup-content {
    margin-bottom: 50px;

    .after-sales-expiration-content {
      .after-sales-expiration-tips {
        font-size: 19px;
        color: #171E24;
        text-align: center;
        font-weight: 700;
        margin-bottom: 15px;
      }

      .after-sales-expiration-sub-tips {
        margin-top: 10px;
        font-size: 13px;
        color: #5A6066;
        text-align: center;
        font-weight: 400;
      }
    }
  }

  .popup-op {
    width: 100%;
    height: 35px;
    margin-top: 20px;

    .popup-op-btn {
      background-image: linear-gradient(97deg, #FFA033 0%, #FF6D33 100%);
      border-radius: 49px;
      font-size: 17px;
      color: #FFFFFF;
      font-weight: 400;
      width: 100%;
      height: 35px;
      text-align: center;
      line-height: 35px;
      cursor: pointer;
    }
  }
}
</style>
