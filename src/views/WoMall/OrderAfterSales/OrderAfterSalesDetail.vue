<template>
  <div class="order-after-sales-detail" v-if="Object.keys(afterSaleDetailInfo).length > 0">
    <div class="after-sales-process" v-if="Object.keys(afterSaleDetailInfo).length > 0">
      <div class="steps"
           v-if="cancelSupplierAfterSalRecStep && afterSaleDetailInfo.afterSaleState !== '0' && afterSaleDetailInfo.afterSaleState !== '7'">
        <div class="steps-item" :class="{ 'active': index < stepIndex }" v-for="(i, index) in stepEnum" :key="i.id">
          <div class="item-top">
            <img v-if="index < stepIndex" class="node-icon selcet" src="./assets/selectNode.png" alt="">
            <img v-else class="node-icon no-select" src="./assets/noSelectNode.png" alt="">
          </div>
          <div class="item-bottom">
            <span class="node-name">{{ i.title }}</span>
          </div>
        </div>
      </div>
      <div class="content" v-if="Object.keys(afterSaleGoodsInfo).length > 0">
        <div class="process-title">
          {{ afterSaleDetailInfo.afterSaleStepCurInfo.tipsTitle }}
        </div>
        <div class="process-tips">
          {{ afterSaleDetailInfo.afterSaleStepCurInfo.tipsSubTitle }}
        </div>
      </div>
    </div>
    <div class="module-divider"></div>
    <div class="logistics-information" v-if="type === '2' && afterSaleDetailInfo.afterSaleState === '3'">
      <div class="business">
        <div class="business-address">
          <div class="base">
            <span>收货地址：{{ afterSaleDetailInfo.returnAddressInfo.recName }}</span><span
            class="phone">{{ afterSaleDetailInfo.returnAddressInfo.recPhone }}</span>
          </div>
          <div class="address">{{ afterSaleDetailInfo.returnAddressInfo.addrDetail }}</div>
          <div class="return-policy">退货说明：{{ afterSaleDetailInfo.returnPolicy ? afterSaleDetailInfo.returnPolicy
            : '影响二次销售禁止退货' }}</div>
        </div>
        <div class="copy" @click="onClipboardCopy(`${afterSaleDetailInfo.returnAddressInfo.recName + afterSaleDetailInfo.returnAddressInfo.recPhone + afterSaleDetailInfo.returnAddressInfo.addrDetail}`)">
          复制
        </div>
      </div>
      <div class="mine-logistics">
        <div class="base">
          <div class="title">
            我已寄出
          </div>
          <div class="tips">
            填写物流单号
          </div>
        </div>
        <div class="input-logistics-btn" @click="setTrackingNumber">
          填写单号
        </div>
      </div>
    </div>
    <div class="module-divider" v-if="type === '2' && afterSaleDetailInfo.afterSaleState === '3'"></div>
    <div class="logistics-compete"
         v-if="type === '2' && (afterSaleDetailInfo.afterSaleState === '4' || afterSaleDetailInfo.afterSaleState === '5')">
      <div class="wo-cell is-center">
        <div class="cell-left">
          <div class="left-title">{{ isLogisticsInfo ? afterSaleDetailInfo.orderTrack : '已填写物流单号' }} </div>
        </div>
        <div class="cell-right" @click="onExpressClick(1)">
          <div class="right-title">查看详情</div>
          <img class="right-arrow" src="./assets/arrow.png" alt="" srcset="">
        </div>
      </div>
    </div>
    <div class="module-divider"
         v-if="type === '2' && (afterSaleDetailInfo.afterSaleState === '4' || afterSaleDetailInfo.afterSaleState === '5')">
    </div>
    <div class="after-sales-info" v-if="Object.keys(afterSaleGoodsInfo).length > 0">
      <div class="info-title">退款信息</div>
      <div class="after-sales-goods">
        <img class="goods-img" :src="afterSaleGoodsInfo.detailImageUrl" alt="" srcset="">
        <div class="goods-info">
          <p class="goods-name">{{ afterSaleGoodsInfo.name }}</p>
          <p class="goods-spec">{{ afterSaleGoodsInfo.spec }}</p>
        </div>
      </div>
      <div class="info-content">
        <div class="info-cell">
          <div class="cell-left">
            退款原因
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.refundReason || afterSaleDetailInfo.rejectReason }}
          </div>
        </div>
        <div class="info-cell">
          <div class="cell-left">
            退款金额
          </div>
          <div class="cell-right">
            ￥{{ afterSaleDetailInfo.afterSaleState !== '6' ? afterSaleDetailInfo.applyRefundMoney :
            afterSaleDetailInfo.refundMoney }}
          </div>
        </div>
        <div class="info-cell">
          <div class="cell-left">
            申请件数
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.applyRefundSkuNum || afterSaleDetailInfo.applyRejectSkuNum }}件
          </div>
        </div>
        <div class="info-cell">
          <div class="cell-left">
            申请时间
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.applyDate }}
          </div>
        </div>
        <div class="info-cell" v-if="afterSaleDetailInfo.cancelDate">
          <div class="cell-left">
            取消时间
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.cancelDate }}
          </div>
        </div>
        <div class="info-cell" v-if="afterSaleDetailInfo.refundDate">
          <div class="cell-left">
            退款时间
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.refundDate }}
          </div>
        </div>
        <div class="info-cell">
          <div class="cell-left">
            服务单号
          </div>
          <div class="cell-right">
            {{ afterSaleDetailInfo.id }}
          </div>
        </div>
        <div class="info-cell" v-if="isJD && afterSaleDetailInfo.supplierOutOrderId">
          <div class="cell-left">
            京东订单号
          </div>
          <div class="cell-right">
             <span>{{ afterSaleDetailInfo.supplierOutOrderId }}</span>
            <span class="copy" @click="copyText(afterSaleDetailInfo.supplierOutOrderId)">复制</span>
          </div>
        </div>
        <div class="info-cell">
          <div class="cell-left">
            支付订单号
          </div>
          <div class="cell-right">
            <span>{{ afterSaleDetailInfo.bizOrderId }}</span>
            <span class="copy" @click="copyText(afterSaleDetailInfo.bizOrderId)">
              复制
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="op" v-if="Object.keys(afterSaleDetailInfo).length > 0 && dataLoaded">
      <WoButton size="medium"  @click="onHotLineClick">联系客服</WoButton>
      <WoButton size="medium"  type="primary"   v-if="showCancelRefundButton" @click="cancelRefund">取消退款</WoButton>
      <WoButton size="medium"  type="primary" v-if="showCancelReturnButton" @click="cancelReturn">取消退货</WoButton>
      <WoButton size="medium"  type="primary"  v-if="showReapplyButton" @click="reapply">重新申请</WoButton>
    </div>

    <Popup class="popup after-sales-expiration-popup" :style="{ minHeight: '240px' }" safe-area-inset-bottom lock-scroll
           round position="bottom" v-model:visible="afterSalesExpirationPopupShow">
      <div class="popup-header">
        <p class="title"></p>
        <img @click="popupClose" class="close" src="./assets/popupClose.png" alt="" srcset="">
      </div>
      <div class="popup-content">
        <div class="after-sales-expiration-content">
          <p class="after-sales-expiration-tips">抱歉，订单已过售后申请时效</p>
          <p class="after-sales-expiration-sub-tips">商品已超过售后期限，如需售后可联系客服处理</p>
        </div>
      </div>
      <div class="popup-op">
        <div class="popup-op-btn" @click="afterSalesExpirationPopupShow = false">
          确定
        </div>
      </div>
    </Popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { cancelAfterSales, getAfterSalesExpress, getOrderAfterSalesInfo } from '@/api/interface/order'
import { getBizCode } from '@/utils/curEnv'
import { fenToYuan } from '@/utils/amount'
import { closeToast, Popup, showLoadingToast, showToast } from 'vant'
import dayjs from 'dayjs'
import { useAfterSalesStore } from '@/store/modules/afterSales'
import useClipboard from 'vue-clipboard3'
import { afterSalesProduct } from '@utils/storage.js'
import { useAlert } from '@/composables/index.js'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
// 步骤枚举定义
const refundStepEnum1 = [
  {
    id: 1,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]
const refundStepEnum2 = [
  {
    id: 2,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]
const returnStepEnum1 = [
  {
    id: 1,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 3,
    title: '寄回商品',
    tipsTitle: '寄回商品',
    tipsSubTitle: '请尽快寄回商品，未与商家协商一致，请勿使用到付或平邮，以免商家拒签货物。'
  },
  {
    id: 5,
    title: '商家退款',
    tipsTitle: '等待商家退款',
    tipsSubTitle: '如果商家收到货并验货无误,将操作退款给您,如果商家拒绝退款请联系客服处理。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]
const returnStepEnum2 = [
  {
    id: 1,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 3,
    title: '寄回商品',
    tipsTitle: '寄回商品',
    tipsSubTitle: '请尽快寄回商品，未与商家协商一致，请勿使用到付或平邮，以免商家拒签货物。'
  },
  {
    id: 4,
    title: '商家退款',
    tipsTitle: '等待商家退款',
    tipsSubTitle: '如果商家收到货并验货无误,将操作退款给您,如果商家拒绝退款请联系客服处理。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]
const returnStepEnum3 = [
  {
    id: 2,
    title: '商家审核',
    tipsTitle: '等待商家审核',
    tipsSubTitle: '您已提交退款申请，请耐心等待商家审核，如遇到快递员派件时请拒收，否则可能造成退款失败。'
  },
  {
    id: 3,
    title: '寄回商品',
    tipsTitle: '寄回商品',
    tipsSubTitle: '请尽快寄回商品，未与商家协商一致，请勿使用到付或平邮，以免商家拒签货物。'
  },
  {
    id: 4,
    title: '商家退款',
    tipsTitle: '等待商家退款',
    tipsSubTitle: '如果商家收到货并验货无误,将操作退款给您,如果商家拒绝退款请联系客服处理。'
  },
  {
    id: 6,
    title: '退款完成',
    tipsTitle: '退款完成',
    tipsSubTitle: '您提交的退款已完成，预计3个工作日内退回原支付账户，请注意查收。'
  }
]
const refundClosedStepEnum = [
  {
    id: 0,
    title: '退款关闭',
    tipsTitle: '退款关闭',
    tipsSubTitle: '因您主动取消退款申请，退款申请已关闭，如问题未解决，可在有效期内再次申请售后。'
  },
  {
    id: 7,
    title: '退款关闭',
    tipsTitle: '退款关闭',
    tipsSubTitle: '商家拒绝退货，有运营人员或者商家填写原因。'
  }
]

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const afterSalesStore = useAfterSalesStore()
const { toClipboard } = useClipboard()
const $alert = useAlert()
// 响应式状态
const stepIndex = ref(1)
const type = ref(route.query.type)
const afterSaleId = ref(route.query.afterSaleId)
const bizCode = ref(getBizCode())
const afterSaleDetailInfo = reactive({
  orderTrack: '',
  cancelSupplierAfterSalRec: {}
})
const afterSaleGoodsInfo = ref([])
const isJD = ref(false)
const isSupplier = ref(false)
const stepEnum = ref([])
const isLogisticsInfo = ref(false)
const afterSalesExpirationPopupShow = ref(false)
const dataLoaded = ref(false)

// 计算属性
const isCancelSupplierAfterSalRec = computed(() => {
  return Object.keys(afterSaleDetailInfo.cancelSupplierAfterSalRec).length > 0
})

const cancelSupplierAfterSalRecStep = computed(() => {
  if (isCancelSupplierAfterSalRec.value) {
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '0') {
      return true
    }
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '1') {
      return false
    }
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '2') {
      return true
    }
  }
  return true
})

const cancelSupplierAfterSalRecBtn = computed(() => {
  if (isCancelSupplierAfterSalRec.value) {
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '0') {
      return false
    }
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '1') {
      return false
    }
    if (afterSaleDetailInfo.cancelSupplierAfterSalRec.RESULT_STATE === '2') {
      return true
    }
  }
  return true
})

const showCancelRefundButton = computed(() => {
  return cancelSupplierAfterSalRecBtn.value &&
    type.value === '1' &&
    afterSaleDetailInfo.afterSaleState !== '0' &&
    afterSaleDetailInfo.afterSaleState !== '7' &&
    afterSaleDetailInfo.afterSaleState !== '6' &&
    !isJD.value && !isSupplier.value
})

const showCancelReturnButton = computed(() => {
  return cancelSupplierAfterSalRecBtn.value &&
    type.value === '2' &&
    afterSaleDetailInfo.afterSaleState !== '0' &&
    afterSaleDetailInfo.afterSaleState !== '7' &&
    afterSaleDetailInfo.afterSaleState !== '6' &&
    !isJD.value && !isSupplier.value
})

const showReapplyButton = computed(() => {
  return cancelSupplierAfterSalRecBtn.value &&
    (afterSaleDetailInfo.afterSaleState === '0' ||
      afterSaleDetailInfo.afterSaleState === '7') &&
    afterSaleDetailInfo.afterSaleState !== '6'
})

// 方法
const popupClose = () => {
  afterSalesExpirationPopupShow.value = false
}

const onHotLineClick = async () => {
  window.location.href = 'https://service.unicompayment.com/live800/chatClient/chatbox.jsp?companyID=9061&configID=47&pagereferrer=%e5%95%86%e5%9f%8e&chatfrom=sc&enterurl=sc&sc=sc'
}

const getExpress = async (applySaleApplyId) => {
  showLoadingToast()
  try {
    const [err, json] = await getAfterSalesExpress(applySaleApplyId)
    closeToast()
    if (!err) return json
    return {}
  } catch (error) {
    closeToast()
    return {}
  }
}

const onExpressClick = async (type = 0) => {
  const { id, supplierOrderId } = afterSaleDetailInfo
  if (type === 0) {
    const orderExpress = await getExpress(id)
    const { orderTrack } = orderExpress
    if (Object.keys(orderExpress).length > 0 && orderTrack && orderTrack.length > 0) {
      const filterLastInfo = orderTrack.shift()
      afterSaleDetailInfo.orderTrack = filterLastInfo.context
      isLogisticsInfo.value = true
    } else {
      isLogisticsInfo.value = false
    }
  }
  if (type === 1) {
    router.push({
      path: '/wo-after-sales-express',
      query: {
        applySaleApplyId: id,
        orderId: supplierOrderId
      }
    })
  }
}

const setTrackingNumber = () => {
  const { id } = afterSaleDetailInfo
  router.push({
    path: '/wo-after-sales-logic-info',
    query: {
      applySaleApplyId: id
    }
  })
}

const getOrderAfterSalesInfoData = async () => {
  showLoadingToast()
  try {
    const [err, json] = await getOrderAfterSalesInfo(afterSaleId.value, bizCode.value)
    closeToast()
    if (!err) {
      Object.assign(afterSaleDetailInfo, json)
      const afterSaleState = afterSaleDetailInfo.stateProcess.split(',').pop()
      afterSaleDetailInfo.afterSaleState = afterSaleState

      if (type.value === '1' || type.value === '2') {
        if (type.value === '1') {
          if (afterSaleDetailInfo.afterSaleState === '1') {
            stepEnum.value = refundStepEnum1
          } else if (afterSaleDetailInfo.afterSaleState === '2') {
            stepEnum.value = refundStepEnum2
          } else {
            stepEnum.value = refundStepEnum1
          }
        } else {
          if (afterSaleDetailInfo.afterSaleState === '5') {
            stepEnum.value = returnStepEnum1
          } else if (afterSaleDetailInfo.afterSaleState === '4') {
            stepEnum.value = returnStepEnum2
          } else if (afterSaleDetailInfo.afterSaleState === '2') {
            stepEnum.value = returnStepEnum3
          } else {
            console.warn(5645564556456)
            if (afterSaleDetailInfo.returnPolicy) {
              const isNo3List = returnStepEnum1.filter(item => item.id !== 3)
              console.warn(isNo3List)
              stepEnum.value = [...isNo3List, {
                id: 3,
                title: '寄回商品',
                tipsTitle: '寄回商品',
                tipsSubTitle: '请尽快寄回商品，未与商家协商一致，请勿使用到付或平邮，以免商家拒签货物'
              }]
            } else {
              stepEnum.value = returnStepEnum1
            }
          }
        }
        const afterSaleStepCurInfo = stepEnum.value.filter(item => item.id === +json.afterSaleState)[0]
        afterSaleDetailInfo.afterSaleStepCurInfo = afterSaleStepCurInfo
      }

      afterSaleDetailInfo.returnPolicy = json.detail.returnPolicy
      afterSaleDetailInfo.applyRefundSkuNum = json.detail.applyRefundSkuNum
      afterSaleDetailInfo.refundReason = json.detail.refundReason
      afterSaleDetailInfo.applyRefundMoney = fenToYuan(json.detail.applyRefundMoney)
      afterSaleDetailInfo.rejectReasonNum = json.detail.rejectReasonNum
      afterSaleDetailInfo.rejectReason = json.detail.rejectReason
      afterSaleDetailInfo.applyRejectSkuNum = json.detail.applyRejectSkuNum
      afterSaleDetailInfo.refundMoney = fenToYuan(json.detail.refundMoney)
      afterSaleDetailInfo.refundDate = json.detail.refundDate

      if (afterSaleDetailInfo.cancelSupplierAfterSalRec.length > 0) {
        const cancelSupplierAfterSalRec = afterSaleDetailInfo.cancelSupplierAfterSalRec[0]
        if (cancelSupplierAfterSalRec.RESULT_STATE === '0') {
          afterSaleDetailInfo.cancelSupplierAfterSalRec = cancelSupplierAfterSalRec
          stepEnum.value[0].tipsSubTitle = '您已经提交取消申请，等待商家确认。'
        } else if (cancelSupplierAfterSalRec.RESULT_STATE === '1') {
          afterSaleDetailInfo.cancelSupplierAfterSalRec = cancelSupplierAfterSalRec
          if (type.value === '1') {
            stepEnum.value[0].tipsTitle = '退款关闭'
          } else if (type.value === '2') {
            stepEnum.value[0].tipsTitle = '退货关闭'
          }
          stepEnum.value[0].tipsSubTitle = '因您主动取消退款申请，退款申请已关闭。如问题未解决，可在有效期内再次申请售后。'
        } else if (cancelSupplierAfterSalRec.RESULT_STATE === '2') {
          stepEnum.value[0].tipsSubTitle = '取消售后失败，继续按原流程进行，如有疑问请联系在线客服。'
          afterSaleDetailInfo.cancelSupplierAfterSalRec = {}
        }
      }

      if (json.detail.returnAddress) {
        const { recName, recPhone, addrDetail } = json.detail.returnAddress
        const returnAddressInfo = {
          recName,
          recPhone,
          addrDetail: `${addrDetail}`
        }
        afterSaleDetailInfo.returnAddressInfo = returnAddressInfo
      } else {
        afterSaleDetailInfo.returnAddressInfo = ''
      }

      // 获取最后一个售后状态，展现在头部，直接拼接到前端数据结构
      afterSaleDetailInfo.lastAfterSalesState = json.afterSaleState[json.afterSaleState.length - 1]
      afterSaleGoodsInfo.value = json.skuInfo

      const params = []
      if (afterSaleGoodsInfo.value.param) params.push(afterSaleGoodsInfo.value.param)
      if (afterSaleGoodsInfo.value.param1) params.push(afterSaleGoodsInfo.value.param1)
      if (afterSaleGoodsInfo.value.param2) params.push(afterSaleGoodsInfo.value.param2)
      if (afterSaleGoodsInfo.value.param3) params.push(afterSaleGoodsInfo.value.param3)
      if (afterSaleGoodsInfo.value.param4) params.push(afterSaleGoodsInfo.value.param4)
      afterSaleGoodsInfo.value.spec = params.join(' ')

      afterSaleGoodsInfo.value.detailImageUrl = afterSaleGoodsInfo.value.detailImageUrl[0]

      stepIndex.value = json.stateProcess.split(',').length
      if (json.afterSaleState === '0' || json.afterSaleState === '7') {
        const stepEnum = refundClosedStepEnum
        const afterSaleStepCurInfo = stepEnum.filter(item => item.id === +json.afterSaleState)[0]
        if (json.afterSaleState === '7' && afterSaleDetailInfo.exmReDes) {
          afterSaleStepCurInfo.tipsSubTitle = afterSaleDetailInfo.exmReDes
        } else if (json.afterSaleState === '7' && !afterSaleDetailInfo.exmReDes) {
          afterSaleStepCurInfo.tipsSubTitle = ''
        }
        afterSaleDetailInfo.afterSaleStepCurInfo = afterSaleStepCurInfo
      }

      isJD.value = afterSaleDetailInfo.supplierCode.indexOf('jd_') > -1
      const codesToCheck = ['zst', 'hzamdzswyxgs', 'bjhbkjyxgs', 'jd_yg', 'jd_wbf']
      isSupplier.value = codesToCheck.some(code => afterSaleDetailInfo.supplierCode.indexOf(code) !== -1)
      dataLoaded.value = true
    }

    if (afterSaleDetailInfo.afterSaleState === '5' || afterSaleDetailInfo.afterSaleState === '4') {
      onExpressClick()
    }
  } catch (error) {
    closeToast()
  }
}

const copyText = async (text) => {
  try {
    await toClipboard(text)
    showToast('复制成功')
  } catch (e) {
    showToast('复制失败')
  }
}

const cancelReturn = () => {
  const cancelOrderFn = async () => {
    showLoadingToast()
    try {
      const [err] = await cancelAfterSales(afterSaleId.value, bizCode.value)
      closeToast()
      if (!err) {
        await getOrderAfterSalesInfoData()
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
    }
  }

  // 使用Vant的Dialog替代原来的$alert
  $alert({
    title: '',
    message: '您确定要取消本次退货吗？如超出售后服务期，将无法再次发起售后申请。',
    confirmButtonText: '取消退货',
    cancelButtonText: '我再想想',
    showCancelButton: true,
    closeOnClickOverlay: true,
    onConfirmCallback: cancelOrderFn
  })
}

const cancelRefund = () => {
  const cancelOrderFn = async () => {
    showLoadingToast()
    try {
      const [err] = await cancelAfterSales(afterSaleId.value, bizCode.value)
      closeToast()
      if (!err) {
        await getOrderAfterSalesInfo()
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
    }
  }

  $alert({
    title: '',
    message: '你确定要取消本次退款吗？如超出售后服务期，将无法再次发起退款申请。',
    confirmButtonText: '取消退款',
    cancelButtonText: '我再想想',
    showCancelButton: true,
    closeOnClickOverlay: true,
    onConfirmCallback: cancelOrderFn
  })
}

const reapply = async () => {
  const { orderDate } = afterSaleDetailInfo
  // 订单时间
  const expDate = dayjs(orderDate).add(15, 'day')
  // 当前时间
  const now = dayjs()
  // 当前订单是否在有效期内
  const isExpires = expDate > now
  if (!isExpires) {
    afterSalesExpirationPopupShow.value = true
    return
  }

  const { applyType, afterSaleState, bizOrderId, bizCode } = afterSaleDetailInfo
  const { orderState } = afterSalesProduct.get()

  if (applyType === '1') {
    if (orderState === '1' || orderState === '3') {
      // 将售后相关信息保存到Vuex中
      afterSalesStore.updateAfterSalesInfo({
        applyType,
        afterSaleState,
        bizOrderId,
        bizCode,
        orderState
      })

      router.go(-1)
    } else {
      router.replace({
        path: '/wo-after-sales-entry',
        query: {
          orderState
        }
      })
    }
  } else if (applyType === '2') {
    router.replace({
      path: '/wo-after-sales-entry',
      query: {
        orderState
      }
    })
  }
}

// 生命周期钩子
onMounted(() => {
  getOrderAfterSalesInfoData()
})
</script>

<style scoped lang="less">
@import '@/assets/css/design-system.less';

.order-after-sales-detail {
  background: rgba(247, 247, 247, 0.80);
  padding-bottom: 50px;
  min-height: 100vh;
  box-sizing: border-box;

  .module-divider {
    width: 100%;
    height: 10px;
    background: rgba(247, 247, 247, 0.80);
  }

  .after-sales-process {
    box-sizing: border-box;
    padding: 17px;
    background-color: @bg-color-white;

    .steps {
      display: flex;
      align-items: center;
      justify-content: center;

      .steps-item {
        position: relative;
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .item-top {
          text-align: center;
          width: 100%;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          .node-icon {
            position: relative;
            width: 18px;
            height: 18px;
            z-index: 60;
          }

          &:before {
            position: absolute;
            top: 50%;
            left: 0;
            content: '';
            width: 50%;
            height: 4px;
            background-color: #DFDFDF;
            transform: translateY(-50%);
          }

          &:after {
            position: absolute;
            top: 50%;
            right: 0;
            content: '';
            width: 50%;
            height: 4px;
            background-color: #DFDFDF;
            transform: translateY(-50%);
          }
        }

        .item-bottom {
          font-size: 0;
          margin-top: 8px;

          .node-name {
            margin-top: 10px;
            font-size: @font-size-13;
            color: @text-color-primary;
            line-height: 1.5;
            font-weight: @font-weight-400;
          }
        }

        &:first-child .item-top:before {
          display: none;
        }

        &:last-child .item-top:after {
          display: none;
        }

        &.active .item-top:before {
          background-color: @color-orange;
        }

        &.active .item-top:after {
          background-color: @color-orange;
        }
      }
    }

    .content {
      margin-top: 12px;

      .process-title {
        font-size: @font-size-18;
        color: @text-color-primary;
        line-height: 1.5;
        font-weight: @font-weight-700;
      }

      .process-tips {
        margin-top: 6px;
        font-size: @font-size-15;
        color: @text-color-secondary;
        line-height: 1.5;
        font-weight: @font-weight-400;
      }
    }
  }

  .logistics-information {
    padding: 17px;
    background-color: @bg-color-white;

    .business {
      display: flex;
      justify-content: space-between;

      .business-address {
        flex: 1;

        .base {
          font-size: @font-size-16;
          color: @text-color-primary;
          line-height: 1.5;
          font-weight: @font-weight-400;

          .phone {
            margin-left: 5px;
            color: @text-color-secondary;
          }
        }

        .address {
          margin-top: 6px;
          font-size: @font-size-12;
          color: @text-color-tertiary;
          line-height: 1.5;
          font-weight: @font-weight-400;
        }

        .return-policy {
          margin-top: 6px;
          font-size: @font-size-12;
          color: @text-color-tertiary;
          line-height: 1.5;
          font-weight: @font-weight-400;
        }
      }

      .copy {
        font-size: @font-size-16;
        margin-left: 5px;
        color: @color-orange;
      }
    }

    .mine-logistics {
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .base {
        .title {
          font-size: @font-size-16;
          color: @text-color-primary;
          line-height: 1;
          font-weight: @font-weight-400;
        }

        .tips {
          margin-top: 6px;
          font-size: @font-size-12;
          color: @text-color-tertiary;
          line-height: 1;
          font-weight: @font-weight-400;
        }
      }

      .input-logistics-btn {
        font-size: @font-size-12;
        color: @text-color-tertiary;
        text-align: center;
        line-height: 30px;
        font-weight: @font-weight-400;
        width: 80px;
        height: 30px;
        border: 1px solid @text-color-tertiary;
        border-radius: 15px;
      }
    }
  }

  .logistics-compete {
    box-sizing: border-box;
    background-color: @bg-color-white;
    padding: 0 17px;

    .wo-cell {
      width: 100%;
      min-height: 55px;
      display: flex;
      justify-content: space-between;
      padding: 15px 0;

      &.is-center {
        align-items: center;
      }

      &.is-border {
        border-bottom: 1px solid rgba(227, 227, 227, 1);
      }

      .is-require {
        position: relative;

        &:after {
          content: '*';
          position: absolute;
          top: -3px;
          right: -10px;
          color: @color-red;
          font-size: 25px;
        }
      }

      .is-vertical {
        flex-direction: column;
      }

      .cell-left {
        min-width: 65px;
        margin-right: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .left-title {
          font-size: @font-size-16;
          color: @text-color-primary;
          line-height: 1;
          font-weight: @font-weight-400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .cell-right {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .right-title {
          font-size: @font-size-15;
          color: @text-color-tertiary;
          line-height: 1;
          font-weight: @font-weight-400;
        }

        .right-arrow {
          margin-left: 5px;
          width: 15px;
          height: 15px;
        }

        .customize-content {
          .content {
            font-size: @font-size-15;
            color: @text-color-primary;
            text-align: right;
            line-height: 1.2;
            font-weight: @font-weight-400;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            white-space: normal;
          }

          .tips {
            margin-top: 8px;
            font-size: @font-size-12;
            color: @text-color-tertiary;
            text-align: right;
            line-height: 1.2;
            font-weight: @font-weight-400;
          }
        }

        .goods-price {
          width: 100%;
          text-align: right;
          display: flex;
          align-items: flex-end;
          justify-content: flex-end;

          .num {
            margin-right: 8px;
            font-size: @font-size-18;
            color: @color-orange;
            line-height: 1;
            font-weight: @font-weight-400;

            .integer {
              font-weight: @font-weight-600;
            }

            .decimal {
              font-size: @font-size-15;
              font-weight: @font-weight-500;
            }

            &:before {
              content: '￥';
              font-size: @font-size-13;
            }
          }

          .edit {
            width: 14px;
            height: 14px;
          }
        }

        .goods-price-tips {
          margin-top: 8px;
          font-size: @font-size-12;
          color: @text-color-tertiary;
          text-align: right;
          line-height: 1;
          font-weight: @font-weight-400;
        }
      }
    }
  }

  .after-sales-info {
    box-sizing: border-box;
    padding: 17px;
    background-color: @bg-color-white;

    .info-title {
      font-size: @font-size-16;
      color: @text-color-primary;
      line-height: 1.5;
      font-weight: @font-weight-400;
      margin-bottom: 10px;
    }

    .after-sales-goods {
      width: 100%;
      box-sizing: border-box;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      background: #fff;

      .goods-img {
        width: 70px;
        height: 70px;
        margin-right: 15px;
      }

      .goods-info {
        flex: 1;
        overflow: hidden;

        .goods-name {
          margin-bottom: 10px;
          font-size: @font-size-15;
          color: @text-color-primary;
          line-height: 1.5;
          font-weight: @font-weight-600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .goods-spec {
          font-size: @font-size-12;
          color: @text-color-secondary;
          line-height: 1.5;
          font-weight: @font-weight-400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .info-content {
      .info-cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .cell-left {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          font-size: @font-size-14;
          color: @text-color-secondary;
          line-height: 1.5;
          font-weight: @font-weight-400;
          min-width: 70px;
          margin-right: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .cell-right {
          flex: 1;
          font-size: @font-size-14;
          color: @text-color-primary;
          text-align: right;
          line-height: 1.5;
          font-weight: @font-weight-400;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .copy {
            margin-left: 5px;
            color: @color-orange;
          }
        }
      }
    }
  }

  .op {
    position: fixed;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    padding: 12px 17px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: @bg-color-white;
    gap: 10px;
    .contact-online-customer {
      display: flex;
      justify-content: center;
      align-items: center;
      color: @color-orange;
      font-size: @font-size-14;
      font-weight: @font-weight-400;

      i {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 5px;
        background-image: url("../assets/customer-service.png");
        background-repeat: no-repeat;
        background-size: contain;
      }
    }
  }
}

.popup {
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .title {
      flex: 1;
      font-size: @font-size-17;
      color: @text-color-primary;
      text-align: center;
      line-height: 1;
      font-weight: @font-weight-400;
    }

    .close {
      width: 14px;
      height: 14px;
    }
  }

  .popup-content {
    margin-bottom: 50px;

    .after-sales-expiration-content {
      .after-sales-expiration-tips {
        font-size: 19px;
        color: @text-color-primary;
        text-align: center;
        font-weight: @font-weight-700;
        margin-bottom: 15px;
      }

      .after-sales-expiration-sub-tips {
        margin-top: 10px;
        font-size: @font-size-13;
        color: @text-color-secondary;
        text-align: center;
        font-weight: @font-weight-400;
      }
    }
  }

  .popup-op {
    width: 100%;
    height: 35px;
    margin-top: 20px;

    .popup-op-btn {
      background-image: @gradient-orange-106;
      border-radius: 49px;
      font-size: @font-size-17;
      color: @bg-color-white;
      font-weight: @font-weight-400;
      width: 100%;
      height: 35px;
      text-align: center;
      line-height: 35px;
    }
  }
}
</style>
