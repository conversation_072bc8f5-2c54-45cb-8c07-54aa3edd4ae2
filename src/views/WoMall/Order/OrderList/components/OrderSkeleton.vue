<template>
  <div class="order-skeleton">
    <div v-for="i in 3" :key="`skeleton-${i}`" class="order-skeleton__item">
      <WoCard>
        <div class="order-skeleton__content">
          <header class="order-skeleton__header">
            <div class="order-skeleton__order-number"></div>
            <div class="order-skeleton__status"></div>
          </header>

          <section class="order-skeleton__goods">
            <div class="order-skeleton__image"></div>
            <div class="order-skeleton__details">
              <div class="order-skeleton__title"></div>
              <div class="order-skeleton__subtitle"></div>
              <div class="order-skeleton__price"></div>
            </div>
          </section>

          <footer class="order-skeleton__actions">
            <div class="order-skeleton__button"></div>
            <div class="order-skeleton__button"></div>
          </footer>
        </div>
      </WoCard>
    </div>
  </div>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'
</script>

<style scoped lang="less">
.order-skeleton {
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    padding: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__order-number {
    width: 120px;
    height: 12px;
    background: #f0f0f0;
    border-radius: @radius-6;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__status {
    width: 60px;
    height: 16px;
    background: #f0f0f0;
    border-radius: @radius-8;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__goods {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  &__image {
    width: 75px;
    height: 75px;
    background: #f0f0f0;
    border-radius: @radius-8;
    margin-right: 12px;
    flex-shrink: 0;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__details {
    flex: 1;
    min-height: 75px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__title {
    width: 80%;
    height: 16px;
    background: #f0f0f0;
    border-radius: @radius-8;
    margin-bottom: 8px;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__subtitle {
    width: 60%;
    height: 12px;
    background: #f0f0f0;
    border-radius: @radius-6;
    margin-bottom: 8px;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__price {
    width: 40%;
    height: 14px;
    background: #f0f0f0;
    border-radius: @radius-6;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  &__button {
    width: 60px;
    height: 28px;
    background: #f0f0f0;
    border-radius: @radius-15;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
</style>
