<template>
  <section class="order-tab-content" ref="contentRef">
    <OrderSkeleton v-if="showSkeleton" />

    <OrderEmptyState v-if="!showSkeleton && !loading && orderList.length === 0 && finished" />

    <van-list
      v-else
      v-model:loading="loading"
      :finished="finished"
      loading-text="加载中..."
      finished-text="没有更多了"
      @load="loadOrderList"
      :immediate-check="false"
    >
      <CommonOrderItem
        v-for="order in orderList"
        :key="order.id"
        :order="order"
        :visible-buttons="getVisibleButtonsForTemplate(order)"
        :more-actions="getMoreActionsForTemplate(order)"
        @detail-click="goToOrderDetail"
        @copy-order="copyOrderNumber"
      />
    </van-list>

    <Certificate
      :show="certificate.show"
      :title="certificate.title"
      :date="certificate.date"
      :amt="certificate.amt"
      @close="onCertificateClose"
    />
  </section>
</template>

<script setup>
import { ref, watch, nextTick, toRefs, onMounted, onUnmounted } from 'vue'

import OrderSkeleton from './OrderSkeleton.vue'
import OrderEmptyState from './OrderEmptyState.vue'
import CommonOrderItem from '../../components/CommonOrderItem.vue'
import Certificate from '../../components/Certificate/Certificate.vue'

import { useOrderList } from '../../composables/useOrderList.js'
import { useOrderActions } from '../../composables/useOrderActions.js'
import { useOrderButtons } from '../../composables/useOrderButtons.js'
import { useOrderCountdown } from '../../composables/useOrderCountdown.js'

const props = defineProps({
  tabType: {
    type: String,
    required: true
  },
  scrollPosition: {
    type: Number,
    default: 0
  }
})

const { tabType, scrollPosition } = toRefs(props)

const emit = defineEmits(['update-scroll'])

// 证书弹窗状态
const certificate = ref({
  show: false,
  title: '',
  date: new Date(),
  amt: ''
})

// 使用通用的订单列表逻辑
const {
  loading,
  finished,
  orderList,
  showSkeleton,
  contentRef,
  loadOrderList,
  resetData,
  refreshData,
  handleScroll,
  removeOrderItem
} = useOrderList({
  tabType
})

// 使用通用的订单操作逻辑
const orderActions = useOrderActions()

// 使用通用的订单按钮逻辑
const { getVisibleButtons, getMoreActions } = useOrderButtons()

// 使用通用的倒计时逻辑
const {
  startCountdown,
  clearAllCountdowns,
  initOrderCountdowns,
  handleVisibilityChange
} = useOrderCountdown()

// 创建操作配置，包含成功回调和证书设置
const createActionConfig = () => ({
  ...orderActions,
  onSuccess: () => {
    resetData()
    loadOrderList()
  },
  setCertificate: (config) => {
    certificate.value = { ...certificate.value, ...config }
  }
})

const actionConfig = createActionConfig()

// 获取按钮配置
const getVisibleButtonsWithActions = (order) => {
  return getVisibleButtons(order, actionConfig)
}

const getMoreActionsWithActions = (order) => {
  return getMoreActions(order, actionConfig)
}

// 重写模板中使用的方法名
const { copyOrderNumber, goToOrderDetail } = orderActions

// 重新定义按钮获取方法以使用正确的名称
const getVisibleButtonsForTemplate = (order) => getVisibleButtonsWithActions(order)
const getMoreActionsForTemplate = (order) => getMoreActionsWithActions(order)

// 处理滚动事件，发送滚动位置给父组件
const handleScrollWithEmit = () => {
  handleScroll()
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    emit('update-scroll', scrollTop)
  }
}

// 监听标签页切换
watch(() => tabType.value, () => {
  clearAllCountdowns()
  resetData()
  nextTick(() => {
    loadOrderList().then(orders => {
      if (orders) {
        initOrderCountdowns(orderList)
      }
    })
  })
})

// 证书弹窗关闭
const onCertificateClose = () => {
  certificate.value.show = false
}

// 组件挂载时的初始化
const initComponent = () => {
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScrollWithEmit)
  } else {
    window.addEventListener('scroll', handleScrollWithEmit)
  }

  document.addEventListener('visibilitychange', () => handleVisibilityChange(orderList))

  loadOrderList().then(orders => {
    if (orders) {
      initOrderCountdowns(orderList)
    }
  })
}

// 组件卸载时的清理
const cleanupComponent = () => {
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScrollWithEmit)
  } else {
    window.removeEventListener('scroll', handleScrollWithEmit)
  }

  document.removeEventListener('visibilitychange', () => handleVisibilityChange(orderList))
  clearAllCountdowns()
}

onMounted(() => {
  initComponent()
})

onUnmounted(() => {
  cleanupComponent()
})

// 暴露刷新方法给父组件
defineExpose({
  refreshData
})

</script>

<style scoped lang="less">
.order-tab-content {
  min-height: calc(100vh - 134px);
  padding: 10px;
}

</style>
