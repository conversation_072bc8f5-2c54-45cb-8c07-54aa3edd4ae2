import { ref, computed, nextTick } from 'vue'
import { debounce, throttle } from 'lodash-es'
import { showLoadingToast, closeToast, showToast } from 'vant'
import { getOrderList, getOrderSearchList } from '@/api/interface/order.js'
import { getBizCode } from '@/utils/curEnv.js'

/**
 * 订单列表相关的通用逻辑
 */
export function useOrderList(options = {}) {
  const {
    isSearch = false,
    tabType = ref('all'),
    searchKeyword = ref(''),
    pageSize = 10
  } = options

  // 基础状态
  const loading = ref(false)
  const finished = ref(false)
  const orderList = ref([])
  const currentPage = ref(1)
  const totalPage = ref(0)
  const error = ref(false)
  const isRefreshing = ref(false)
  const showSkeleton = ref(false)
  const contentRef = ref(null)

  // 滚动位置管理
  const scrollPositions = ref({ all: 0 })

  const showSkeletonComputed = computed(() => {
    return showSkeleton.value || (loading.value && orderList.value.length === 0 && !error.value)
  })

  /**
   * 处理供应商订单数据
   */
  const processSupplierOrders = (order) => {
    if (!order.supplierOrderList?.length) {
      return order
    }

    let mergedSkuNumInfoList = []
    let totalPrice = 0

    order.supplierOrderList.forEach(supplierOrder => {
      if (supplierOrder.skuNumInfoList?.length) {
        mergedSkuNumInfoList = [...mergedSkuNumInfoList, ...supplierOrder.skuNumInfoList]
      }
      if (supplierOrder.price) {
        totalPrice += parseFloat(supplierOrder.price) || 0
      }
    })

    return {
      ...order,
      skuNumInfoList: mergedSkuNumInfoList,
      price: totalPrice > 0 ? totalPrice.toString() : order.price,
      totalPrice: totalPrice > 0 ? totalPrice.toString() : order.totalPrice
    }
  }

  /**
   * 加载订单列表
   */
  const loadOrderList = async () => {
    const params = {
      disriBiz: getBizCode('ORDER'),
      pageNum: currentPage.value,
      pageSize: pageSize
    }

    if (isSearch) {
      params.searchContent = searchKeyword.value
    } else {
      params.orderState = tabType.value
    }

    // 第一页显示骨架屏
    if (currentPage.value === 1 && orderList.value.length === 0) {
      showSkeleton.value = true
    } else {
      loading.value = true
    }

    const apiCall = isSearch ? getOrderSearchList : getOrderList
    const [err, json] = await apiCall(params)

    if (!err) {
      currentPage.value++
      loading.value = false
      showSkeleton.value = false

      if (json && json?.list.length <= 0) {
        finished.value = true
        return
      }

      const newOrders = json.list || []
      const expandedOrders = newOrders.map(processSupplierOrders)

      if (isRefreshing.value) {
        orderList.value = expandedOrders
        isRefreshing.value = false
      } else if (currentPage.value === 2) {
        orderList.value = expandedOrders
      } else {
        orderList.value = [...orderList.value, ...expandedOrders]
      }

      // 添加 showPopover 属性
      orderList.value.forEach(item => {
        if (!item.hasOwnProperty('showPopover')) {
          item.showPopover = false
        }
      })

      totalPage.value = json.totalPage
      if (currentPage.value > totalPage.value) {
        finished.value = true
      }

      // 恢复滚动位置
      if (currentPage.value === 2 && scrollPositions.value.all > 0) {
        nextTick(() => {
          if (contentRef.value) {
            contentRef.value.scrollTop = scrollPositions.value.all
          }
        })
      }

      return expandedOrders
    } else {
      error.value = true
      loading.value = false
      finished.value = true
      showSkeleton.value = false
      if (isSearch) {
        showToast('搜索失败，请重试')
      }
      throw err
    }
  }

  /**
   * 重置数据
   */
  const resetData = () => {
    orderList.value = []
    currentPage.value = 1
    finished.value = false
    error.value = false
    loading.value = false
    isRefreshing.value = false
    showSkeleton.value = false
  }

  /**
   * 刷新数据
   */
  const refreshData = async () => {
    try {
      isRefreshing.value = true
      resetData()
      loading.value = true
      await loadOrderList()
    } catch (error) {
      console.error('刷新数据失败:', error)
      isRefreshing.value = false
      throw error
    }
  }

  /**
   * 搜索订单
   */
  const searchOrders = async (keyword) => {
    if (!keyword && isSearch) {
      showToast('请输入搜索关键词')
      return
    }

    searchKeyword.value = keyword
    showSkeleton.value = true
    resetData()

    nextTick(() => {
      loadOrderList()
    })
  }

  /**
   * 滚动处理
   */
  const updateScrollPosition = (position) => {
    scrollPositions.value.all = position
  }

  const handleScroll = throttle(() => {
    if (contentRef.value) {
      const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
      updateScrollPosition(scrollTop)
    }
  }, 100)

  /**
   * 删除订单项
   */
  const removeOrderItem = (orderId) => {
    const index = orderList.value.findIndex(order => order.id === orderId)
    if (index !== -1) {
      orderList.value[index].isDeleting = true
      setTimeout(() => {
        orderList.value.splice(index, 1)
      }, 500)
    }
  }

  /**
   * 防抖搜索
   */
  const debouncedSearch = debounce(searchOrders, 300)

  return {
    // 状态
    loading,
    finished,
    orderList,
    currentPage,
    totalPage,
    error,
    isRefreshing,
    showSkeleton: showSkeletonComputed,
    contentRef,
    scrollPositions,

    // 方法
    loadOrderList,
    resetData,
    refreshData,
    searchOrders,
    debouncedSearch,
    handleScroll,
    updateScrollPosition,
    removeOrderItem,
    processSupplierOrders
  }
}
