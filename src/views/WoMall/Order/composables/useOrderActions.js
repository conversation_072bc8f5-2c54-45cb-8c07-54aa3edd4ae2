import { showToast, showLoadingToast, closeToast } from 'vant'
import { useRouter } from 'vue-router'
import { useAlert } from '@/composables/index.js'
import useClipboard from 'vue-clipboard3'
import dayjs from 'dayjs'
import { formSubmit } from 'commonkit'
import {
  cancelOrder,
  modOrderListShow,
  manualConfirmRecv,
  repayOrder,
  getOrderExpress,
  verifySupplierOrderRepurchased
} from '@/api/interface/order.js'
import { getBizCode } from '@/utils/curEnv.js'
import { buyProductCart, buyProductCartSession } from '@/utils/storage.js'
import { fenToYuan } from '@/utils/amount.js'

/**
 * 订单操作相关的通用逻辑
 */
export function useOrderActions() {
  const router = useRouter()
  const $alert = useAlert()
  const { toClipboard } = useClipboard()

  /**
   * 复制订单号
   */
  const copyOrderNumber = async (orderNumber) => {
    try {
      await toClipboard(orderNumber)
      showToast('复制成功')
    } catch (e) {
      console.error(e)
      showToast('复制失败')
    }
  }

  /**
   * 跳转到订单详情
   */
  const goToOrderDetail = (order) => {
    router.push({
      path: '/user/order/detail',
      query: {
        orderId: order.id,
        isPay: order.orderState === '0' ? '1' : '2'
      }
    })
  }

  /**
   * 取消订单
   */
  const cancelOrderAction = async (bizOrderId, onSuccess) => {
    const cancelOrderFn = async () => {
      showLoadingToast()
      const [err] = await cancelOrder(bizOrderId)
      closeToast()
      if (!err) {
        showToast('取消成功')
        onSuccess && onSuccess()
      } else {
        showToast(err.msg)
      }
    }

    $alert({
      title: '',
      message: '取消后将无法恢复，您确定要取消订单吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: cancelOrderFn
    })
  }

  /**
   * 删除订单
   */
  const deleteOrderAction = async (item, onSuccess) => {
    const deleteOrderFn = async () => {
      showLoadingToast()

      try {
        if (item.supplierOrderList?.length > 0) {
          const deletePromises = item.supplierOrderList.map(supplierOrder => {
            return modOrderListShow({
              supplierOrderId: supplierOrder.id,
              isDelete: 1
            })
          })

          const results = await Promise.all(deletePromises)
          const hasError = results.some(([err]) => err)

          if (hasError) {
            const errorResult = results.find(([err]) => err)
            throw new Error(errorResult[0].msg)
          }
        } else {
          const [err] = await modOrderListShow({
            supplierOrderId: item.id,
            isDelete: 1
          })
          if (err) {
            throw new Error(err.msg)
          }
        }

        closeToast()
        showToast('删除成功')
        onSuccess && onSuccess()
      } catch (error) {
        closeToast()
        showToast(error.message || '删除失败')
      }
    }

    $alert({
      title: '',
      message: '确认删除该订单？',
      confirmButtonText: '确定',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: deleteOrderFn
    })
  }

  /**
   * 支付订单
   */
  const payOrderAction = async (bizOrderId) => {
    showLoadingToast()
    try {
      const [res, json] = await repayOrder(bizOrderId)
      closeToast()

      if (res.code === '0000') {
        formSubmit(json.wapURL, { param: json.encryptContent })
      } else if (res.code === '2091070302' && res.data?.length > 0) {
        if (json.some(info => info.state === '2')) {
          showToast('您的订单中有商品已下架')
        } else if (json.some(info => info.state === '3')) {
          showToast('您的订单中有无货商品')
        } else if (json.some(info => info.state === '4')) {
          showToast('您的订单中有商品库存不足')
        }
      } else {
        showToast(res.msg)
      }
    } catch (error) {
      closeToast()
      console.error('支付失败:', error)
      showToast('支付失败，请重试')
    }
  }

  /**
   * 确认收货
   */
  const confirmReceiptAction = async (item, onSuccess) => {
    const confirmReceiptFn = async () => {
      showLoadingToast()

      try {
        if (item.supplierOrderList?.length > 0) {
          const confirmPromises = item.supplierOrderList.map(supplierOrder => {
            return manualConfirmRecv({ supplierOrderId: supplierOrder.id })
          })

          const results = await Promise.all(confirmPromises)
          const hasError = results.some(([err]) => err)

          if (hasError) {
            const errorResult = results.find(([err]) => err)
            throw new Error(errorResult[0].msg)
          }
        } else {
          const [err] = await manualConfirmRecv({ supplierOrderId: item.id })
          if (err) {
            throw new Error(err.msg)
          }
        }

        closeToast()
        showToast('确认收货成功')
        onSuccess && onSuccess()
      } catch (error) {
        closeToast()
        showToast(error.message || '操作失败')
      }
    }

    $alert({
      title: '确认收货',
      message: '确认收到商品了吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      messageAlign: 'center',
      onConfirmCallback: confirmReceiptFn
    })
  }

  /**
   * 查看物流
   */
  const viewLogisticsAction = async (item) => {
    const { id, orderDate } = item
    const now = dayjs()
    const orderDateDayjs = dayjs(orderDate)
    const endTimeSub180 = now.subtract(12, 'month')
    const isWithinScope = orderDateDayjs.isBefore(endTimeSub180, 'minute')

    if (isWithinScope) {
      showToast('物流信息已失效 ！')
      return
    }

    try {
      const [err, orderExpress] = await getOrderExpress(id)

      if (err) {
        showToast('查询物流信息失败')
        return
      }

      const { orderPackageList } = orderExpress
      if (orderPackageList?.length > 0) {
        router.push({
          name: 'user-order-entry-express',
          params: { orderExpress },
          query: { orderId: id }
        })
        return
      }

      showToast('物流信息已失效 ！')
    } catch (error) {
      console.error('查询物流信息失败:', error)
      showToast('查询物流信息失败')
    }
  }

  /**
   * 香蕉树
   */
  const bananaTreeAction = () => {
    router.push('/fpHome/banana-tree')
  }

  /**
   * 荣誉证书
   */
  const certificateAction = (item, setCertificate) => {
    setCertificate({
      date: new Date(item.orderDate.replace(/-/g, '/')),
      title: '尊敬的沃钱包用户：',
      amt: fenToYuan(item.paymentDetail.actualPayAmount),
      show: true
    })
  }

  /**
   * 催发货
   */
  const urgeShipmentAction = (item) => {
    const { orderDate } = item
    const targetDate = dayjs(orderDate)
    const now = dayjs()
    const diff = now.diff(targetDate, 'millisecond')
    const diffInHours = diff / (1000 * 60 * 60)
    const isWithin48Hours = Math.abs(diffInHours) <= 48

    if (isWithin48Hours) {
      const dateAdd48 = targetDate.add(48, 'hour')
      const formattedDate = dateAdd48.format('M月DD日')
      $alert({
        messageHtml: `<div>您的商品目前处于正常配送时效内，商家将于<span style="color:#FF780A;">${formattedDate}</span>前发货，请您耐心等待。</div>`,
        confirmButtonText: '确定',
        allowHtml: true,
        messageAlign: 'center'
      })
    } else {
      $alert({
        message: '给您带来的不便深感抱歉，已为您提醒商家发货，请您耐心等待。',
        confirmButtonText: '确定',
        messageAlign: 'center'
      })
    }
  }

  /**
   * 再次购买
   */
  const buyAgainAction = async (orderInfo) => {
    const bizCode = getBizCode('ORDER')
    showLoadingToast()

    try {
      let allValidGoodsList = []
      let totalSkuCount = 0

      if (orderInfo.supplierOrderList?.length > 0) {
        const verifyPromises = orderInfo.supplierOrderList.map(supplierOrder => {
          return verifySupplierOrderRepurchased({
            bizCode,
            supplierOrderId: supplierOrder.id
          })
        })

        const results = await Promise.all(verifyPromises)

        results.forEach(([err, res], index) => {
          if (!err && res?.validGoodsList) {
            allValidGoodsList = [...allValidGoodsList, ...res.validGoodsList]
          }
          const supplierOrder = orderInfo.supplierOrderList[index]
          if (supplierOrder.skuNumInfoList) {
            totalSkuCount += supplierOrder.skuNumInfoList.length
          }
        })
      } else {
        const [err, res] = await verifySupplierOrderRepurchased({
          bizCode,
          supplierOrderId: orderInfo.id
        })

        if (!err && res) {
          allValidGoodsList = res.validGoodsList || []
        }
        totalSkuCount = orderInfo.skuNumInfoList?.length || 0
      }

      closeToast()

      if (allValidGoodsList.length === 0) {
        showToast('订单中的商品都卖光了，在看看其他商品吧~')
        return
      }

      buyProductCart.set(allValidGoodsList)
      buyProductCartSession.set(allValidGoodsList)

      if (totalSkuCount === allValidGoodsList.length) {
        router.push('/orderconfirm')
      } else if (totalSkuCount > allValidGoodsList.length) {
        $alert({
          title: '',
          message: '部分商品无货或已下架无法购买!',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          onConfirmCallback: () => router.push('/orderconfirm')
        })
      }
    } catch (error) {
      closeToast()
      console.error('再次购买检查失败:', error)
      showToast('操作失败，请重试')
    }
  }

  return {
    copyOrderNumber,
    goToOrderDetail,
    cancelOrderAction,
    deleteOrderAction,
    payOrderAction,
    confirmReceiptAction,
    viewLogisticsAction,
    bananaTreeAction,
    certificateAction,
    urgeShipmentAction,
    buyAgainAction
  }
}
