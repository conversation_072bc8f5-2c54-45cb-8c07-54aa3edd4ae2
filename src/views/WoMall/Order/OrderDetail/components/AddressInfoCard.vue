<template>
  <WoCard>
    <div class="address-info-card">
      <div class="address-info-card__header">
        <div class="address-info-card__user">
          <img src="@/static/images/address-icon.png" alt="地址图标" class="address-info-card__user-icon" />
          <span class="address-info-card__user-name">{{ receiverName }}</span>
          <span class="address-info-card__user-phone">{{ receiverPhone }}</span>
        </div>
        <div class="address-info-card__action">
          <div class="address-info-card__action-button">
            <img src="@/static/images/mod-address-icon.png" alt="地址编辑" class="address-info-card__action-icon" />
            修改地址
          </div>
        </div>
      </div>
      <div class="address-info-card__content">
        <div class="address-info-card__detail">
          {{ fullAddress }}
        </div>
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import { toRefs } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'

const props = defineProps({
  receiverName: {
    type: String,
    default: ''
  },
  receiverPhone: {
    type: String,
    default: ''
  },
  fullAddress: {
    type: String,
    default: ''
  }
})

const { receiverName, receiverPhone, fullAddress } = toRefs(props)
</script>

<style scoped lang="less">
.address-info-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;

  &__header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    overflow: hidden;
  }

  &__content {
    width: 100%;
  }

  &__user {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: @font-size-16;
    color: @text-color-primary;
    font-weight: @font-weight-500;
    line-height: 1.5;
    margin-right: 8px;
    overflow: hidden;
  }

  &__user-icon {
    width: 13px;
    height: 15px;
    margin-right: 8px;
  }

  &__user-name {
    margin-right: 12px;
    .ellipsis();
  }

  &__action {
    font-size: @font-size-14;
    font-weight: @font-weight-500;
    line-height: 1.5;
  }

  &__action-button {
    display: flex;
    align-items: center;
    color: @theme-color;
    cursor: pointer;
  }

  &__action-icon {
    margin-right: 4px;
    width: 13px;
    height: 13px;
    vertical-align: middle;
  }

  &__detail {
    font-size: @font-size-12;
    color: @text-color-secondary;
    font-weight: @font-weight-400;
    line-height: 1.5;
    .multi-ellipsis(2);
  }
}
</style>
