<template>
  <div class="goods-skeleton">
    <div class="goods-skeleton__item">
      <div class="goods-skeleton__image"></div>
      <div class="goods-skeleton__info">
        <div class="skeleton-line goods-skeleton__name"></div>
        <div class="skeleton-line goods-skeleton__spec"></div>
        <div class="goods-skeleton__bottom">
          <div class="skeleton-line goods-skeleton__price"></div>
          <div class="skeleton-line goods-skeleton__quantity"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped lang="less">
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: @radius-4;
}

.goods-skeleton {
  background-color: @bg-color-white;
  border-radius: @radius-10;
  padding: 12px;

  &__item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid @divider-color-base;

    &:last-child {
      border-bottom: none;
    }
  }

  &__image {
    width: 65px;
    height: 65px;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: @radius-6;
    margin-right: 12px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__name {
    height: 18px;
    width: 80%;
    margin-bottom: 8px;
  }

  &__spec {
    height: 14px;
    width: 60%;
    margin-bottom: 12px;
  }

  &__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__price {
    height: 16px;
    width: 80px;
  }

  &__quantity {
    height: 14px;
    width: 40px;
  }
}
</style>
