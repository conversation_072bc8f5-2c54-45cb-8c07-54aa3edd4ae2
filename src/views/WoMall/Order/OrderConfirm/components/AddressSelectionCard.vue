<template>
  <section class="address-selection">
    <div class="address-selection__card" @click="handleSelect">
      <div v-if="isComplete" class="address-selection__content">
        <div class="address-selection__region">{{ address.region }}</div>
        <div class="address-selection__detail">{{ address.detailAddress }}</div>
        <div class="address-selection__contact">
          <span class="address-selection__name">{{ address.name }}</span>
          <span class="address-selection__phone">{{ address.phone }}</span>
        </div>
      </div>
      <div v-else class="address-selection__placeholder">
        <div class="address-selection__icon">
          <img src="@/static/images/location.png" alt="地址" class="address-selection__location-icon" loading="lazy" />
        </div>
        <div class="address-selection__placeholder-content">
          <div class="address-selection__title">请选择收货地址</div>
          <div class="address-selection__subtitle">选择收货地址后才能下单</div>
        </div>
      </div>
      <img src="@/static/images/arrow-right-gray.png" alt="选择地址" class="address-selection__arrow" loading="lazy" />
    </div>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'
import AddressSkeleton from './AddressSkeleton.vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  address: {
    type: Object,
    default: () => ({})
  },
  isComplete: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['select'])

const { loading, address, isComplete } = toRefs(props)

const handleSelect = () => {
  emit('select')
}
</script>

<style scoped lang="less">
.address-selection {
  background-color: @bg-color-white;
  margin-bottom: 8px;
  border-radius: @radius-10;

  &__card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 13px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }

  &__content {
    flex: 1;
  }

  &__region {
    font-size: @font-size-13;
    color: @text-color-tertiary;
    line-height: 1.5;
    margin-bottom: 2px;
  }

  &__detail {
    font-size: @font-size-16;
    color: @text-color-primary;
    font-weight: @font-weight-600;
    line-height: 1.5;
    margin-bottom: 8px;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  &__contact {
    display: flex;
    align-items: center;
    font-size: @font-size-13;
    color: @text-color-tertiary;
    line-height: 1.5;
  }

  &__name {
    margin-right: 12px;
  }

  &__placeholder {
    flex: 1;
    display: flex;
    align-items: center;

    &-content {
      flex: 1;
    }
  }

  &__icon {
    margin-right: 10px;
    flex-shrink: 0;
  }

  &__location-icon {
    width: 14px;
    height: 15px;
    display: block;
  }

  &__title {
    font-size: @font-size-16;
    color: @text-color-secondary;
    font-weight: @font-weight-500;
    margin-bottom: 2px;
    line-height: 1.4;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  &__subtitle {
    font-size: @font-size-13;
    color: @text-color-tertiary;
    line-height: 1.4;
  }

  &__arrow {
    margin-left: 10px;
    width: 6px;
    height: 12px;
    flex-shrink: 0;
    opacity: 0.6;
  }
}
</style>
