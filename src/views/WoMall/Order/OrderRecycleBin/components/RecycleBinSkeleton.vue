<template>
  <div class="recycle-bin-skeleton">
    <div v-for="i in 3" :key="`skeleton-${i}`" class="recycle-bin-skeleton__item">
      <WoCard>
        <div class="recycle-bin-skeleton__content">
          <div class="recycle-bin-skeleton__header">
            <div class="recycle-bin-skeleton__order-number"></div>
            <div class="recycle-bin-skeleton__status"></div>
          </div>
          <div class="recycle-bin-skeleton__goods">
            <div class="recycle-bin-skeleton__image"></div>
            <div class="recycle-bin-skeleton__info">
              <div class="recycle-bin-skeleton__title"></div>
              <div class="recycle-bin-skeleton__subtitle"></div>
              <div class="recycle-bin-skeleton__price"></div>
            </div>
          </div>
          <div class="recycle-bin-skeleton__actions">
            <div class="recycle-bin-skeleton__button"></div>
            <div class="recycle-bin-skeleton__button"></div>
          </div>
        </div>
      </WoCard>
    </div>
  </div>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'
</script>

<style scoped lang="less">
.recycle-bin-skeleton {
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    padding: 15px;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  &__order-number {
    width: 120px;
    height: 14px;
    background: #f0f0f0;
    border-radius: @radius-4;
  }

  &__status {
    width: 60px;
    height: 14px;
    background: #f0f0f0;
    border-radius: @radius-4;
  }

  &__goods {
    display: flex;
    margin-bottom: 15px;
  }

  &__image {
    width: 75px;
    height: 75px;
    background: #f0f0f0;
    border-radius: @radius-8;
    margin-right: 12px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
  }

  &__title {
    width: 80%;
    height: 16px;
    background: #f0f0f0;
    border-radius: @radius-4;
    margin-bottom: 8px;
  }

  &__subtitle {
    width: 60%;
    height: 14px;
    background: #f0f0f0;
    border-radius: @radius-4;
    margin-bottom: 8px;
  }

  &__price {
    width: 40%;
    height: 14px;
    background: #f0f0f0;
    border-radius: @radius-4;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  &__button {
    width: 60px;
    height: 28px;
    background: #f0f0f0;
    border-radius: @radius-4;
  }
}
</style>
