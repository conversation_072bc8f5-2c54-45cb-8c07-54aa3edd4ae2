<template>
  <main class="order-callback">
    <section class="order-callback__content">
      <div class="order-callback__status">
        <span class="order-callback__status-icon" aria-label="支付成功图标"/>
        <h1 class="order-callback__status-text">支付完成</h1>
      </div>
      <div class="order-callback__actions">
        <WoButton
          size="medium"
          @click="handleGoHome">
          返回首页
        </WoButton>
        <WoButton
          type="primary"
          size="medium"
          @click="handleViewOrder">
          查看订单
        </WoButton>
      </div>
      <p class="order-callback__tips">正在为您备货，请在我的订单中查询物流详情。</p>
    </section>
  </main>
</template>

<script setup>
import { useRouter } from 'vue-router'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'

const router = useRouter()

const handleGoHome = () => {
  router.push('/home')
}

const handleViewOrder = () => {
  router.push({
    path: '/user/order/list',
    query: { type: 3 }
  })
}
</script>

<style scoped lang="less">
@import '@/assets/css/design-system.less';

.order-callback {
  min-height: 100vh;
  background-color: @bg-color-white;

  &__content {
    padding: 25px 16px;
    text-align: center;
  }

  &__status {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
  }

  &__status-icon {
    display: block;
    width: 28px;
    height: 28px;
    background-image: url(./assets/icon-success.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    will-change: transform;
  }

  &__status-text {
    margin: 0 0 0 7px;
    font-size: @font-size-17;
    font-weight: @font-weight-500;
    color: @text-color-primary;
    line-height: 1.4;
  }

  &__actions {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 16px;
  }

  &__tips {
    margin: 0;
    font-size: @font-size-13;
    color: @text-color-secondary;
    line-height: 1.5;
  }
}
</style>
