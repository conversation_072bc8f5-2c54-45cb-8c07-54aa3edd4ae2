<template>
  <button 
    type="button"
    class="history-item"
    @click="handleClick"
  >
    <span class="history-item__text">{{ keyword }}</span>
  </button>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
  keyword: {
    type: String,
    required: true
  }
})

const { keyword } = toRefs(props)

const emit = defineEmits(['click'])

const handleClick = () => {
  emit('click', keyword.value)
}
</script>

<style scoped lang="less">
.history-item {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background-color: @bg-color-gray;
  border: none;
  border-radius: @radius-2;
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 200px;

  &:hover {
    opacity: @opacity-07;
  }

  &:active {
    transform: scale(0.98);
  }

  &__text {
    font-size: @font-size-13;
    color: @text-color-secondary;
    .ellipsis();
  }
}
</style>
