<template>
  <main class="order-search">
    <SearchHeader
      ref="searchHeaderRef"
      v-model="searchKeyword"
      placeholder="搜索商品"
      @search="handleSearch"
    />
    <SearchHistory
      v-if="historyRecords.length > 0"
      :records="historyRecords"
      @use-keyword="useHistoryKeyword"
      @clear-all="clearAllHistory"
    />
  </main>
</template>
<script setup>
import SearchHeader from '@components/Common/SearchHeader.vue'
import SearchHistory from './components/SearchHistory.vue'
import { onMounted, ref, toRefs } from 'vue'
import { delHistoryRecord, getOrderSearchHistory } from '@api/interface/search.js'
import { useRoute, useRouter } from 'vue-router'
import { get, debounce } from 'lodash-es'
import { closeToast, showLoadingToast } from 'vant'

const route = useRoute()
const router = useRouter()
const { query } = toRefs(route)

const searchKeyword = ref('')
const historyRecords = ref([])
const searchHeaderRef = ref(null)

const fetchHistoryRecords = async () => {
  showLoadingToast()
  const [err, data] = await getOrderSearchHistory()
  closeToast()
  if (!err && data) {
    historyRecords.value = data
  }
}

const useHistoryKeyword = (keyword) => {
  searchKeyword.value = keyword
}

const clearAllHistory = async () => {
  const [err] = await delHistoryRecord({
    type: 'ALL'
  })
  if (!err) {
    historyRecords.value = []
  }
}

const handleSearch = debounce(() => {
  const testDMX = get(query.value, 'testDMX', false)
  const trimmedKeyword = searchKeyword.value?.trim()
  if (trimmedKeyword) {
    router.push({
      path: '/user/order/searchList',
      query: {
        keyword: trimmedKeyword,
        testDMX
      }
    })
    fetchHistoryRecords()
  }
}, 300)

onMounted(() => {
  fetchHistoryRecords()
  searchHeaderRef.value?.inputRef?.focus()
})
</script>
<style scoped lang="less">
.order-search {
  min-height: 100vh;
  background-color: @bg-color-white;
}
</style>
