<template>
  <section class="express-timeline">
    <ol class="express-timeline__list">
      <li 
        class="express-timeline__item" 
        v-for="(item, index) in trackingData"
        :key="`track-${index}-${item.msgTime || item.time}`"
        :class="{ 'express-timeline__item--active': index === 0 }"
      >
        <div class="express-timeline__content">{{ item.content || item.context }}</div>
        <time class="express-timeline__time">{{ item.msgTime || item.time }}</time>
      </li>
    </ol>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
  trackingData: {
    type: Array,
    default: () => []
  }
})

const { trackingData } = toRefs(props)
</script>

<style lang="less" scoped>
.express-timeline {
  padding: 15px 15px 15px 56px;
  width: 100%;
  background: @bg-color-white;
  box-sizing: border-box;
  contain: layout style;

  &__list {
    margin: 0;
    padding: 0;
    list-style: none;
    transform: translateZ(0);
  }

  &__item {
    position: relative;
    padding-bottom: 22px;
    font-size: @font-size-14;
    line-height: 20px;
    text-align: left;
    color: @text-color-tertiary;

    &::before {
      content: '';
      position: absolute;
      z-index: 2;
      left: -32px;
      display: block;
      width: 8px;
      height: 8px;
      background-color: @text-color-disabled;
      border: 2px solid @bg-color-white;
      border-radius: 50%;
    }

    &::after {
      content: '';
      position: absolute;
      z-index: 1;
      left: -26px;
      top: 0;
      display: block;
      width: 1px;
      height: 100%;
      background: @divider-color-base;
    }

    &--active {
      color: @text-color-primary;

      &::before {
        left: -34px;
        width: 12px;
        height: 12px;
        background-color: @color-orange;
        border: 3px solid #ffd6b5;
      }
    }

    &:last-child::after {
      display: none;
    }
  }

  &__content {
    display: block;
    margin-bottom: 4px;
    word-break: break-word;
  }

  &__time {
    display: block;
    color: @text-color-tertiary;
    font-size: @font-size-12;
  }
}
</style>
