<template>
  <div class="multi-express-notice">
    <i class="multi-express-notice__icon"></i>
    <span class="multi-express-notice__text">您订单中的商品已拆成{{ packageCount }}个包裹发出</span>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
  packageCount: {
    type: Number,
    required: true
  }
})

const { packageCount } = toRefs(props)
</script>

<style lang="less" scoped>
.multi-express-notice {
  display: flex;
  align-items: center;
  padding: 7px 9px;
  background: #FFF5EC;

  &__icon {
    width: 20px;
    height: 20px;
    background-image: url(../assets/icon-ring.png);
    background-repeat: no-repeat;
    background-size: 100%;
    flex-shrink: 0;
  }

  &__text {
    margin-left: 2.5px;
    font-size: @font-size-13;
    color: @color-orange;
    line-height: 1.4;
  }
}
</style>
