<template>
  <main class="multi-express">
    <MultiExpressSkeleton v-if="isLoading" :item-count="2" />

    <template v-else>
      <MultiExpressNotice
        v-if="shouldShowNotice"
        :package-count="expressList.length"
      />

      <div class="multi-express__list">
        <PackageItem
          v-for="(item, index) in expressList"
          :key="`package-${index}`"
          :package-data="item"
          :index="index"
          :is-j-d-package="isJDGoods(item)"
          :status-text="getStatusText(item)"
          @click="handleExpressClick"
        />
      </div>
    </template>
  </main>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, shallowRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showLoadingToast, closeToast } from 'vant'
import { getOrderExpress } from '@/api/interface/order'
import orderState from '@/utils/orderState'
import { JD_GOODS_CODE } from '@utils/types.js'
import { get, debounce, memoize } from 'lodash-es'
import MultiExpressSkeleton from './components/MultiExpressSkeleton.vue'
import MultiExpressNotice from './components/MultiExpressNotice.vue'
import PackageItem from './components/PackageItem.vue'

const route = useRoute()
const router = useRouter()

const orderId = ref('')
const orderExpress = shallowRef({})
const isLoading = ref(true)

const expressList = computed(() => {
  if (!orderExpress.value || typeof orderExpress.value !== 'object') return []

  const orderPackageList = get(orderExpress.value, 'orderPackageList', [])
  const notDelivered = get(orderExpress.value, 'notDelivered', [])
  return [...orderPackageList, ...notDelivered]
})

const shouldShowNotice = computed(() => expressList.value.length > 1)

const JD_CODE_REGEX = new RegExp(JD_GOODS_CODE)

const isJDGoods = memoize((item) => {
  const supplierCode = get(item, 'supplierSubOrderList[0].supplier.code', '')
  return JD_CODE_REGEX.test(supplierCode)
})

const getOrderExpressData = async () => {
  try {
    showLoadingToast()
    const [err, json] = await getOrderExpress(orderId.value)
    closeToast()

    if (!err && json) {
      return json
    }
    return {}
  } catch (error) {
    console.error('获取订单快递信息失败:', error)
    return {}
  }
}

const handleExpressClick = debounce((index) => {
  const item = expressList.value[index]
  const deliverInfo = get(item, 'deliverInfo')

  if (deliverInfo?.expressName) {
    router.push({
      name: 'user-order-express',
      query: {
        orderId: orderId.value,
        supplierSubOrderId: deliverInfo.supplierSubOrderId,
        expressNo: deliverInfo.expressNo
      }
    })
  }
}, 300)

const EXPRESS_STATE_MAP = new Map([
  ['0', '运输中'],
  ['1', '已揽件'],
  ['2', '疑难件'],
  ['3', '已签收'],
  ['4', '退签'],
  ['5', '派送中'],
  ['6', '退回'],
  ['7', '转单'],
  ['10', '待清关'],
  ['11', '清关中'],
  ['12', '已清关'],
  ['13', '清关异常'],
  ['14', '收件人拒签']
])

const toExpressState = memoize((expressState) =>
  EXPRESS_STATE_MAP.get(String(expressState)) || '待揽件'
)

const toJDExpressState = memoize((state) => orderState(state))

const getStatusText = (item) => {
  if (isJDGoods(item)) {
    return toJDExpressState(item.orderState)
  }

  const expressState = get(item, 'deliverInfo.expressState')
  if (expressState && toExpressState(expressState)) {
    return toExpressState(expressState)
  }

  return ''
}

onMounted(async () => {
  orderId.value = route.query.orderId
  const orderExpressParam = route.params.orderExpress

  if (orderExpressParam) {
    orderExpress.value = orderExpressParam
    isLoading.value = false
  } else {
    try {
      const data = await getOrderExpressData()
      orderExpress.value = data
    } finally {
      isLoading.value = false
    }
  }
})

onUnmounted(() => {
  closeToast()
})
</script>

<style lang="less" scoped>
.multi-express {
  background-color: #f7f7f7cc;
  min-height: 100vh;
  contain: layout style;

  &__list {
    margin-top: -10px;
    padding: 0 10px;
  }
}
</style>
